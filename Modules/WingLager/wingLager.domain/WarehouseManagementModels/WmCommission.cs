
using WingCore.domain.Models;
using wingPrinterListLabel.domain;

namespace wingLager.domain.WarehouseManagementModels;

public record WmCommission
{
    public WmOrder? Order { get; set; }
    public bool WithRobot { get; set; } = false;
    public List<WmVbr> CommissionTrack { get; set; } = [];
    public Kunden? Customer { get; set; } = null;

    public void Reset()
    {
        Order = null;
        WithRobot = false;
        CommissionTrack = [];
        Customer = null;
    }
};
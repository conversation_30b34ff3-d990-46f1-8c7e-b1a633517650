using MediatR;
using wingLager.application.Contracts.LindeQueueRepository;

namespace wingLager.application.LindeQueue.UpdateQueueEntry;

public record UpdateQueueEntryCommandHandler(ILindeQueueRepository Repository) : IRequestHandler<UpdatesQueueEntryCommand>
{
    public async Task Handle(UpdatesQueueEntryCommand request, CancellationToken cancellationToken)
    {
        await Repository.UpdateAsync(request.Entry, cancellationToken);
    }
}
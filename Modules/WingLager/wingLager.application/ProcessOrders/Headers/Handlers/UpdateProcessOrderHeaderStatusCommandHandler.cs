using MediatR;
using wingLager.application.Contracts;

namespace wingLager.application.ProcessOrders.Headers.Handlers;

public class UpdateProcessOrderHeaderStatusCommandHandler(IProcessOrderHeaderRepository repository) : IRequestHandler<UpdateProcessOrderHeaderStatusCommand>
{
    public async Task Handle(UpdateProcessOrderHeaderStatusCommand request, CancellationToken cancellationToken)
    {
        var processOrderHeader = await repository.GetByIdAsync(request.Id, false);
        if(processOrderHeader is null)
            return;
        processOrderHeader.Status = request.Status;
        await repository.Update(processOrderHeader,"System");
    }
}
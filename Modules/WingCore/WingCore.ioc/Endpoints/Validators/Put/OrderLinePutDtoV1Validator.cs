using FluentValidation;
using WingCore.application.ApiDtos.OrderData;
using WingCore.application.ApiDtos.OrderData.Put;

namespace WingCore.ioc.Endpoints.Validators.Put;

public class OrderLinePutDtoV1Validator: AbstractValidator<OrderLinePutDtoV1>
{
    public OrderLinePutDtoV1Validator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.Status).NotEmpty().Must(e => OrderLineDtoV1Status.All.Contains(e))
            .WithMessage("Invalid value for 'Status'. Allowed values are: "+ 
                         string.Join(", ", OrderLineDtoV1Status.All));
        RuleFor(x => x.QuantityLoaded).NotEmpty();
    }
    
}
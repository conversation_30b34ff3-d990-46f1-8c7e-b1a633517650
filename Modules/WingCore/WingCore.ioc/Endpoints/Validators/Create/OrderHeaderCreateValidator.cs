using FluentValidation;
using WingCore.application.ApiDtos.OrderData.Create;

namespace WingCore.ioc.Endpoints.Validators.Create;

public class OrderHeaderCreateValidator : AbstractValidator<OrderHeaderCreateDtoV1>
{
    public OrderHeaderCreateValidator()
    {
        RuleFor(x => x.CustomerNumber).NotEmpty();
        RuleFor(x => x.DeliveryDate).NotEmpty();
        RuleFor(x => x.Category).NotEmpty();
        RuleFor(x => x.Category)
            .Must(e => OrderCategories.All.Contains(e))
            .WithMessage("Invalid value for 'Category'. Allowed values are: " + 
                         string.Join(", ", OrderCategories.All));

        
        
        RuleFor(x => x.OrderLineList)
            .NotEmpty()
            .WithMessage("At least one Position is necessary");

        RuleForEach(x => x.OrderLineList)
            .SetValidator(new OrderLineCreateValidator());
    }
}
using System.Net;
using Commons;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using WingCore.application.Contract.IModels.StammDb;
using WingCore.domain.Api.Auth;

namespace WingCore.ioc.Middlewares;

public class ApiAuthMiddleware(RequestDelegate next)
{

    public async Task InvokeAsync(HttpContext context, IApiKeyRepository apiKeyRepository)
    {
        if (!context.Request.Headers.TryGetValue(AuthConstants.ApiKeyHeaderName, out var extractedApiKey))
            throw new ApiException("API Key missing");
        
        var apiKey = await apiKeyRepository.GetApiKey(extractedApiKey.ToString());    

        if(string.IsNullOrWhiteSpace(apiKey?.Key))
            throw new ApiException("API Key missing");

        await next(context);
    }
};

public class ApiException(string message) : Exception(message), IApiException
{
    public int StatusCode => (int)HttpStatusCode.Unauthorized;
}


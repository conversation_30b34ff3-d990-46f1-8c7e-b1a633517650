using MediatR;
using WingCore.application.Contract.IModels.Helper;
using WingCore.application.Contract.Services;
using WingCore.domain.Models;

namespace WingCore.application.Kunde.Handler;

public class GetCustomerByIdQueryHandler(IKundenService kundenService) : IRequestHandler<GetCustomerByIdQuery, Kunden>
{
    public async Task<Kunden> Handle(GetCustomerByIdQuery request, CancellationToken cancellationToken)
    {
        var kunde = (await kundenService.GetCustomer(false, new CustomerSearchParam{Number = request.Id})).ToList();
        if (kunde.FirstOrDefault() is null)
            throw new Exception($"Kunde mit ID {request.Id} wurde nicht gefunden");
        return kunde.First();
    }
}
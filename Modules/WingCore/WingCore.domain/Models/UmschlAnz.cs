namespace WingCore.domain.Models;

public partial class UmschlAnz
{
    public long DepNr { get; set; }

    public long? KundNr { get; set; }

    public string? KundName { get; set; }

    public string? PlzOrt { get; set; }

    public DateTime? Datum { get; set; }

    public string? Kfznr { get; set; }

    public long? ScheinNr { get; set; }

    public string? FreistNr { get; set; }

    public string? FremdLsnr { get; set; }

    public decimal? Menge { get; set; }

    public decimal? Feuchte { get; set; }

    public bool KlMgZuschl { get; set; }

    public bool KammVerw { get; set; }

    public long Id { get; set; }
}

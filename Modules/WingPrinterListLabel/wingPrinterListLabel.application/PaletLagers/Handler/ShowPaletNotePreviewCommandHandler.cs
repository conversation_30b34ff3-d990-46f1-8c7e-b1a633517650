using MediatR;
using PrinterService;
using wingLager.application.PrintableDtos;
using wingPrinterListLabel.application.Contracts;

namespace wingPrinterListLabel.application.PaletLagers.Handler;

public class ShowPaletNotePreviewCommandHandler(IListLabelRepository listLabelRepository) : IRequestHandler<ShowPaletNotePreviewCommand, string>
{
    public Task<string> Handle(ShowPaletNotePreviewCommand request, CancellationToken cancellationToken)
    {
        var base64 =  PrinterCreator.PdfAsBase64(PalletNoteDto.Create(request.PaletLagerList, request.CommissionCustomer, request.Auftragskopf,
            request.AuftragsposList),request.Layout, request.AppFolder, listLabelRepository);
        return Task.FromResult(base64);
    }
}
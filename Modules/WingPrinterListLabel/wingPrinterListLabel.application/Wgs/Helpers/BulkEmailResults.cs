namespace wingPrinterListLabel.application.Wgs.Helpers;

// Result classes for bulk operations
public record BulkWgsSendEmailResult
{
    public int TotalProcessed { get; init; }
    public int SuccessCount { get; init; }
    public int FailedCount { get; init; }
    public int SkippedCount { get; init; }
    public List<WgsEmailSendResult> Results { get; init; } = [];
}

public record WgsEmailSendResult
{
    public long WgsNumber { get; init; }
    public string Email { get; init; } = string.Empty;
    public bool Success { get; init; }
    public string? ErrorMessage { get; init; }
}

// Job logging result class
public record LogInfoForEWgs
{
    public long WgsNumber { get; set; }
    public string Email { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrMsg { get; set; }
}

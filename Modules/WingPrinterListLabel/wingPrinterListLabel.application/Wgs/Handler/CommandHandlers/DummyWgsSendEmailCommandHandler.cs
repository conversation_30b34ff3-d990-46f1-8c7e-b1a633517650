using MediatR;
using PrinterService;
using PrinterService.Models;
using WingCore.application.Contract.IModels;
using WingCore.application.PrintableDtos;
using WingCore.application.Wiegeschein;
using WingCore.domain.Common.Configurations;
using wingPrinterListLabel.application.Contracts;

namespace wingPrinterListLabel.application.Wgs.Handler.CommandHandlers;


public class DummyWgsSendEmailCommandHandler(IApplicationPath applicationPath,
                                             IListLabelRepository lListLabelRepository) : IRequestHandler<DummyWgsSendEmailCommand, EmailDataToCreateEmail?>
{
    public async Task<EmailDataToCreateEmail?> Handle(DummyWgsSendEmailCommand command, CancellationToken cancellationToken)
    {
        var (eWgsConfig,emailBodyParameter) = await GetEWgsEmailConfig(command.ConfigurationElectricWgs, command.LanguageToUse);
        
        var emailDataToCreateEmail = new EmailDataToCreateEmail
        {
            PrintableObject = LabelWiegeschein.Dummy(),
            LayoutFileWithPath = emailBodyParameter.LayoutForPrint,
            AppFolder = applicationPath.GetPath(),
            EmailParameter = new EmailParameter()
                                {
                                    BodyTemplate = emailBodyParameter.EmailBodyHtml,
                                    FileName = emailBodyParameter.FileName,
                                    Subject = emailBodyParameter.Subject,
                                    
                                    SmtpSecureConnection = eWgsConfig.SmtpSecureConnection,
                                    SmtpSenderAddress = eWgsConfig.SmtpSenderAddress,
                                    SmtpSenderName = eWgsConfig.SmtpSenderName,
                                    SmtpServerAddress = eWgsConfig.SmtpServerAddress,
                                    SmtpServerPort = eWgsConfig.SmtpServerPort,
                                    SmtpServerUser = eWgsConfig.SmtpServerUser,
                                    SmtpServerPassword = eWgsConfig.SmtpServerPassword,
                                    SmtpSocketTimeout = eWgsConfig.SmtpSocketTimeout,
                                    To = command.Email
                                }
    
        };
        
        // Just attach as PDF, no XML needed for WGS
        PrinterCreator.SetEmailDataForSendWithPdf(ref emailDataToCreateEmail,
                                            "",
                                            "",
                                                    lListLabelRepository);
        
        return emailDataToCreateEmail;
    }
    
    private Task<(ConfigurationElectricWgs,LanguageAndEmailFields)> GetEWgsEmailConfig(ConfigurationElectricWgs configurationElectricWgs,
                                                                                       string languageToUse)
    {
        LanguageAndEmailFields? emailBodyParameter = null;
        var userdCountry = new List<string>();
        if (!string.IsNullOrWhiteSpace(languageToUse))
        {
            userdCountry.Add(languageToUse);
            configurationElectricWgs.LanguageToEmailFields.TryGetValue(languageToUse, out emailBodyParameter);
        }
        
        if (emailBodyParameter is null)
        {
            userdCountry.Add("EN");
            configurationElectricWgs.LanguageToEmailFields.TryGetValue("EN", out emailBodyParameter);
        }

        if (emailBodyParameter is null)
            throw new Exception($"Es existiert keine E-Mail konfiguration für das Land '{string.Join(",", userdCountry)}'");

        return Task.FromResult((configurationElectricWgs,emailBodyParameter));
    }
}

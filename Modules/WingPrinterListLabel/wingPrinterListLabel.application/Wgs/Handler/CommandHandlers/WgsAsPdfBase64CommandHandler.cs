using MediatR;
using PrinterService;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.Services;
using WingCore.application.PrintableDtos;
using WingCore.application.Wiegeschein;
using WingCore.domain.Common.Configurations;
using WingCore.domain.Models;
using wingPrinterListLabel.application.Contracts;

namespace wingPrinterListLabel.application.Wgs.Handler.CommandHandlers;

public class WgsAsPdfBase64CommandHandler(ISender mediatr,
                                          IApplicationPath applicationPath,
                                          IConfigurationService configurationService,
                                          IListLabelRepository lListLabelRepository) : IRequestHandler<WgsAsPdfBase64Command, string>
{
    public async Task<string> Handle(WgsAsPdfBase64Command command, CancellationToken cancellationToken)
    {
        var wgs = await mediatr.Send(new WgsWithDataQuery(command.WgsNumber, true), cancellationToken);

        if (wgs is null)
            return string.Empty;

        var (eWgsConfig, emailBodyParameter) = await GetEWgsEmailConfig(command.LanguageToUse);

        var pdfBase64 = PrinterCreator.PdfAsBase64(
            LabelWiegeschein.Create(wgs),
            emailBodyParameter.LayoutForPrint,
            applicationPath.GetPath(),
            lListLabelRepository);

        return pdfBase64;
    }

    private async Task<(ConfigurationElectricWgs, LanguageAndEmailFields)> GetEWgsEmailConfig(string languageToUse)
    {
        var eWgsConfig = await configurationService.GetConfigurationElectricWgsAsync();
        if (eWgsConfig is null)
            throw new Exception("Es existiert keine E-Mail konfiguration für E-Wiegescheine");

        LanguageAndEmailFields? emailBodyParameter = null;
        var usedCountry = new List<string>();
        if (!string.IsNullOrWhiteSpace(languageToUse))
        {
            usedCountry.Add(languageToUse);
            eWgsConfig.LanguageToEmailFields.TryGetValue(languageToUse, out emailBodyParameter);
        }

        if (emailBodyParameter is null)
        {
            usedCountry.Add("EN");
            eWgsConfig.LanguageToEmailFields.TryGetValue("EN", out emailBodyParameter);
        }

        if (emailBodyParameter is null)
            throw new Exception($"Es existiert keine E-Mail konfiguration für die Länder '{string.Join(",", usedCountry)}'");

        return (eWgsConfig, emailBodyParameter);
    }
}

@using Licencing
@using QuartzScheduler
@using WingCore.application.Contract.Services
@using WingCore.domain.Common.Configurations
@using WinGng.RazorLib.Components.Pages.Scheduler

@inject SeedData SeedData


<RadzenRow Gap="1rem">
    <RadzenColumn Size="12" SizeMD="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px" Style="text-align: left;">
            <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                <HeaderTemplate>
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                        <b>@Localizer["Email"]</b>
                    </RadzenStack>
                </HeaderTemplate>
                <ChildContent>
                    <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                        <RadzenRow Gap="1rem" style="font-size: large">
                            <RadzenColumn Size="12" SizeSM="6">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                    <RadzenLabel Text=@Localizer["SenderName"]/>
                                    <RadzenTextBox @bind-Value="@ConfigurationElectricWgs.SmtpSenderName" Style="width:95%"/>
                                </RadzenStack>
                            </RadzenColumn>
                            <RadzenColumn Size="12" SizeSM="6">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                    <RadzenLabel Text=@Localizer["SenderEmail"]/>
                                    <RadzenTextBox @bind-Value="@ConfigurationElectricWgs.SmtpSenderAddress" Style="width:95%"/>
                                </RadzenStack>
                            </RadzenColumn>
                        </RadzenRow>
                        <RadzenRow Gap="1rem" style="font-size: large">
                            <RadzenColumn Size="12" SizeSM="6">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                    <CascadingAuthenticationState>
                                        <AuthorizeView Policy="@LicenseClaimType.SchedulerValue">
                                            <RadzenButton Icon="calendar_clock" Click="CreateScheduler" ButtonStyle="ButtonStyle.Primary" Text=@Localizer["CreateJob"]/>
                                        </AuthorizeView>
                                    </CascadingAuthenticationState>
                                </RadzenStack>
                            </RadzenColumn>
                            <RadzenColumn Size="12" SizeSM="6">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                 </RadzenStack>
                            </RadzenColumn>
                        </RadzenRow>
                    </RadzenStack>
                </ChildContent>
            </RadzenFieldset>
        </RadzenStack>
    </RadzenColumn>
    <RadzenColumn Size="12" SizeMD="6">
        <RadzenStack Orientation="Orientation.Vertical" Gap="6px" Style="text-align: left;">
            <RadzenFieldset AllowCollapse="true" >
                <HeaderTemplate>
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem" >
                        <b>SMTP</b>
                    </RadzenStack>
                </HeaderTemplate>
                <ChildContent>
                    <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                        <RadzenRow Gap="1rem" style="font-size: large">
                            <RadzenColumn Size="12" SizeSM="6">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px" >
                                    <RadzenLabel Text=@Localizer["Server"] />
                                    <RadzenTextBox @bind-Value="@ConfigurationElectricWgs.SmtpServerAddress" Style="width:95%"/>
                                </RadzenStack>
                            </RadzenColumn>
                            <RadzenColumn Size="12" SizeSM="6">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                    <RadzenLabel Text=@Localizer["Port"] />
                                    <RadzenNumeric @bind-Value="@ConfigurationElectricWgs.SmtpServerPort" Style="width:95%"/>
                                </RadzenStack>
                            </RadzenColumn>
                        </RadzenRow>
                        <RadzenRow Gap="1rem" style="font-size: large">
                            <RadzenColumn Size="12" SizeSM="6">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                    <RadzenLabel Text=@Localizer["User"] />
                                    <RadzenTextBox @bind-Value="@ConfigurationElectricWgs.SmtpServerUser" Style="width:95%"/>
                                </RadzenStack>
                            </RadzenColumn>
                            <RadzenColumn Size="12" SizeSM="6">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                    <RadzenLabel Text=@Localizer["Password"] />
                                    <RadzenPassword @bind-Value="@ConfigurationElectricWgs.SmtpServerPassword" Style="width:95%"/>
                                </RadzenStack>
                            </RadzenColumn>
                        </RadzenRow>
                        <RadzenRow Gap="1rem" style="font-size: large">
                            <RadzenColumn Size="12" SizeSM="6">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                    <RadzenLabel Text=@Localizer["TimeoutInMilliseconds"] />
                                    <RadzenNumeric @bind-Value="@ConfigurationElectricWgs.SmtpSocketTimeout" Style="width:95%"/>
                                </RadzenStack>
                            </RadzenColumn>
                            <RadzenColumn Size="12" SizeSM="6" Style=" display: flex; justify-content: left;align-items: center;" >
                                <RadzenStack Orientation="Orientation.Horizontal" Gap="6px">
                                    <RadzenLabel Text="SSL" />
                                    <RadzenSwitch @bind-Value="@ConfigurationElectricWgs.SmtpSecureConnection" />
                                </RadzenStack>
                            </RadzenColumn>
                        </RadzenRow>
                    </RadzenStack>
                </ChildContent>
            </RadzenFieldset>
        </RadzenStack>
    </RadzenColumn>
</RadzenRow>
            


@code {
    [Parameter] public ConfigurationElectricWgs ConfigurationElectricWgs { get; set; } = new();
    [Parameter] public DialogService DialogService { get; set; } = default!;
    
    async Task CreateScheduler()
    {
        try
        {
            var jobViewModel = new JobViewModel() with
            {
                Gruppe = "EWGS",
                SelectedJobClass = SeedData.JobsNameSendEWgsPerMailJob
            };

            JobViewModel? data = await DialogService.OpenAsync<CreateJob>(Localizer["CreateJob"],
                new Dictionary<string, object>
                {
                    { "JobViewModel", jobViewModel }
                },
                new DialogOptions()
                {
                    Width = "30%",
                    Height = "40%",
                    ShowClose = true,
                    Resizable = false,
                    Draggable = true
                });

            if (data?.TimeIntervallInSec is null)
                return;
            
            var result = await SeedData.CheckAndCreateJobsData(
                data.Name,
                data.Gruppe,
                data.ServerName,
                data.TimeIntervallInSec.Value,
                data.SelectedJobClass);
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
}

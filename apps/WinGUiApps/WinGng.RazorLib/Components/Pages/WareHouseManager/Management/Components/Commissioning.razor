@page "/Commissioning"

@using MediatR
@using PrinterService
@using WingCore.application.Aufträge.Auftragspositionen
@using WingCore.application.Contract.IModels
@using WingCore.application.Contract.Services
@using WingCore.application.Kunde
@using WingCore.domain.Common.Configurations
@using wingLager.application.LindeQueue.CreateQueueEntry
@using wingLager.application.PaletLagers
@using wingLager.application.PrintableDtos
@using wingLager.application.ProcessOrders.Headers.Notifications
@using wingLager.application.WarehouseManagement.Commands
@using wingLager.application.WarehouseManagement.Queries
@using wingLager.domain.WarehouseManagementModels
@using WinGng.RazorLib.Components.Common.LoadingPage
@using WinGng.RazorLib.Components.Pages.PrintView
@using WinGng.RazorLib.Components.Pages.WareHouseManager.Management.Components.CommissioningComponents
@using wingPrinterListLabel.application.Contracts
@using wingPrinterListLabel.application.PaletLagers
@using wingPrinterListLabel.application.Printers
@using wingPrinterListLabel.application.RepositoryItems
@using wingPrinterListLabel.domain

@inject ISender Sender
@inject IApplicationPath ApplicationPath
@inject IListLabelFactory ListLabelFactory
@inject DialogService DialogService
@inject IPublisher Publisher
@inject IConfigurationService ConfigurationService

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="ShowPrintView"
                  Option="_printPdfViewOptions"
                  DialogService="DialogService"/>
<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="ShowPalettPrintView"
                  Option="_printPdfViewOptions"
                  DialogService="DialogService"/>
<LoadingIndicator @ref="_onInitialize"
                  DoLoadDataCallback="InitializedAsync"
                  Option="_onInitOptions"
                  DialogService="DialogService"/>

<RadzenStack>
    <RadzenRow>
        <RadzenHeading Size="H1" style="display: inline-block" Text=@Localizer["Commission"]/>
    </RadzenRow>
    <RadzenTabs RenderMode="TabRenderMode.Client">
        <Tabs>
            <RadzenTabsItem Text=@Localizer["Commission"]>
                <RadzenTemplateForm TItem="WmCommission" Data="@Commission" Submit="@OnSubmit">
                    <RadzenStack Orientation="Orientation.Vertical" Gap="2rem">
                        <RadzenRow Style="max-width: 700px;">
                            <RadzenColumn Size="4">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                    <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-2 rz-my-0"
                                                Style="color: var(--rz-text-tertiary-color);width: 100%">@Localizer["Order"]
                                    </RadzenText>
                                    <OrderDataGridDropDown
                                        Name="order"
                                        Data="@OrderList"
                                        ValueChanged="(value) => OnOrderSelected(value)"/>
                                </RadzenStack>
                            </RadzenColumn>
                            <RadzenColumn Size="4" Offset="1">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                    <RadzenStack Orientation="Orientation.Horizontal">
                                        <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-2 rz-my-0"
                                                    Style="color: var(--rz-text-tertiary-color);width: 100%">@Localizer["CommissionTrack"]
                                        </RadzenText>
                                    </RadzenStack>
                                    <RadzenStack Orientation="Orientation.Vertical">
                                        <RadzenDropDown Name="CommissionTrack" TValue="List<WmVbr>" Data="@VbrList"
                                                        TextProperty="@nameof(WmVbr.Number)"
                                                        @bind-Value="@Commission.CommissionTrack" AllowClear="true"
                                                        Multiple="true"
                                                        Placeholder="Spur auswählen..."
                                                        Disabled="@(Commission.Order is null)"
                                                        Style="width:100%; max-width: 300px;"/>

                                        @if (Commission.Order is not null)
                                        {
                                            var availableStellplaetze = Commission.CommissionTrack?.Sum(c => c.StellplatzCount) ?? 0;
                                            var statusColor = availableStellplaetze >= _neededStellplaetze ? "var(--rz-success)" : "var(--rz-danger)";

                                            <RadzenStack Orientation="Orientation.Horizontal" Gap="8px"
                                                         Style="margin-top: 8px; padding: 8px; background-color: var(--rz-base-200); border-radius: 4px; max-width: 300px">
                                                <RadzenText
                                                    Style="font-size: 0.875rem; color: var(--rz-text-secondary-color);">
                                                    Benötigt: <strong>@_neededStellplaetze</strong>
                                                </RadzenText>
                                                <RadzenText
                                                    Style="font-size: 0.875rem; color: var(--rz-text-secondary-color);">
                                                    |
                                                </RadzenText>
                                                <RadzenText Style="@($"font-size: 0.875rem; color: {statusColor};")">
                                                    Verfügbar: <strong>@availableStellplaetze</strong>
                                                </RadzenText>
                                                @if (availableStellplaetze < _neededStellplaetze)
                                                {
                                                    <RadzenIcon Icon="warning"
                                                                Style="color: var(--rz-danger); font-size: 1rem;"/>
                                                }
                                                else if (availableStellplaetze >= _neededStellplaetze && _neededStellplaetze > 0)
                                                {
                                                    <RadzenIcon Icon="check_circle"
                                                                Style="color: var(--rz-success); font-size: 1rem;"/>
                                                }
                                            </RadzenStack>
                                        }
                                    </RadzenStack>
                                </RadzenStack>
                            </RadzenColumn>
                            <RadzenColumn Style="margin-top: 10px">
                                <RadzenStack Orientation="Orientation.Horizontal">
                                    <RadzenCheckBox Name="isInWarehouse" @bind-Value="@Commission.WithRobot"/>
                                    <RadzenText Text=@Localizer["WithRobot"]/>
                                </RadzenStack>
                            </RadzenColumn>

                        </RadzenRow>
                        <RadzenRow AlignItems="AlignItems.Start" Style="max-width: 1000px;">
                            <RadzenColumn Size="3">
                                <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large"
                                              Icon="arrow_forward"
                                              Text=@Localizer["CommissionButton"]
                                              Style="max-width: 300px; margin-bottom: 1rem;"/>

                            </RadzenColumn>
                            <RadzenColumn Size="3">
                                <RadzenButton Size="ButtonSize.Large" Icon="print"
                                              Text=@Localizer["Printer"]
                                              Style="max-width: 300px; margin-bottom: 1rem;" Click="@Print"/>
                            </RadzenColumn>
                        </RadzenRow>
                    </RadzenStack>
                </RadzenTemplateForm>
            </RadzenTabsItem>
            <RadzenTabsItem Text=@Localizer["PrintSettings"]>
                <RadzenStack Size="6" Offset="1">
                    <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save"
                                  Text=@Localizer["SaveConfiguration"] Click="@SaveConfigData"
                                  Style="max-width: 300px; margin-bottom: 1rem;"/>
                    <RadzenStack Orientation="Orientation.Vertical">
                        <RadzenStack Orientation="Orientation.Horizontal"
                                     Style="max-width: 800px;" Gap="6px">
                            <RadzenStack Orientation="Orientation.Vertical" Style="width: 100%">

                                <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-2 rz-my-0"
                                            Style="color: var(--rz-text-tertiary-color);width: 100%">@Localizer["PrintLayoutForDeliveryNote"]
                                </RadzenText>
                                <RadzenStack Orientation="Orientation.Horizontal" Style="width: 100%">
                                    <RadzenColumn>
                                        <RadzenDropDown Name="layoutfordelivery"
                                                        @bind-Value="@ConfigurationCommission.LayoutForDeliveryNote"
                                                        Data="@_allLayoutForDelivery"
                                                        TextProperty="@nameof(CustomizedRepositoryItem.UIName)"
                                                        ValueProperty="@nameof(CustomizedRepositoryItem.InternalID)"
                                                        AllowClear="true"
                                                        Placeholder="Drucklayout auswählen"
                                                        Style="width: 300px;"/>
                                    </RadzenColumn>
                                    <RadzenColumn Style="width: 100%">
                                        <RadzenButton Click="@(_ => OpenDesignerForLayout())" Icon="edit"
                                                      ButtonStyle="ButtonStyle.Primary"/>
                                        <RadzenButton Icon="preview"
                                                      Click="@(() => ShowPrintViewWithLoading(ConfigurationCommission.LayoutForDeliveryNote))"
                                                      ButtonStyle="ButtonStyle.Primary"/>
                                    </RadzenColumn>
                                </RadzenStack>
                                <RadzenCustomValidator Component="layoutfordelivery"
                                                       Text=@Localizer["SelectPrintLayout"]
                                                       Validator="() => !string.IsNullOrEmpty(ConfigurationCommission.LayoutForDeliveryNote)"/>
                            </RadzenStack>
                            <RadzenStack Orientation="Orientation.Vertical" Style="width: 100%">
                                <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-2 rz-my-0"
                                            Style="color: var(--rz-text-tertiary-color);width: 100%">@Localizer["PrintLayoutForPalletNote"]
                                </RadzenText>
                                <RadzenStack Orientation="Orientation.Horizontal" Style="width: 100%">
                                    <RadzenColumn>
                                        <RadzenDropDown Name="layoutforpalett"
                                                        @bind-Value="@ConfigurationCommission.LayoutForPallet"
                                                        Data="@_allLayoutForPalett"
                                                        TextProperty="@nameof(CustomizedRepositoryItem.UIName)"
                                                        ValueProperty="@nameof(CustomizedRepositoryItem.InternalID)"
                                                        AllowClear="true"
                                                        Placeholder="Drucklayout auswählen"
                                                        Style="width: 300px;"/>
                                    </RadzenColumn>
                                    <RadzenColumn Style="width: 100%">
                                        <RadzenButton Click="@(_ => OpenDesignerForPalettLayout())"
                                                      Icon="edit"
                                                      ButtonStyle="ButtonStyle.Primary"/>
                                        <RadzenButton Icon="preview"
                                                      Click="@(() => ShowPalettPrintViewWithLoading(ConfigurationCommission.LayoutForPallet))"
                                                      ButtonStyle="ButtonStyle.Primary"/>
                                    </RadzenColumn>
                                </RadzenStack>
                                <RadzenCustomValidator Component="layoutforpalett"
                                                       Text=@Localizer["SelectPrintLayout"]
                                                       Validator="() => !string.IsNullOrEmpty(ConfigurationCommission.LayoutForPallet)"/>
                            </RadzenStack>
                        </RadzenStack>
                        <RadzenStack Orientation="Orientation.Vertical" Style="width: 100%" Gap="6px">
                            <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-2 rz-my-0"
                                        Style="color: var(--rz-text-tertiary-color);width: 100%">@Localizer["Printer"]
                            </RadzenText>
                            <RadzenDropDown
                                @bind-Value="@ConfigurationCommission.Printer"
                                Data="@_possiblePrinter" Placeholder="Drucker auswählen" AllowClear="true"
                                Style="max-width: 300px"/>
                        </RadzenStack>
                    </RadzenStack>
                </RadzenStack>
            </RadzenTabsItem>
        </Tabs>
    </RadzenTabs>
    <OrderInfo Commission="Commission" Visible="@(Commission.Order is not null)"/>
</RadzenStack>

@code {
    private List<WmOrder> OrderList { get; set; } = [];
    private List<WmVbr> VbrList { get; set; } = [];
    private WmCommission Commission { get; set; } = new();
    private List<CustomizedRepositoryItem> _allLayoutForDelivery = [];
    private List<CustomizedRepositoryItem> _allLayoutForPalett = [];
    private LoadingIndicator? _loadingIndicator = default!;
    private LoadingIndicator? _onInitialize = default!;
    private List<string> _possiblePrinter = [];
    private string Layouttoshow { get; set; } = string.Empty;
    private ConfigurationCommission ConfigurationCommission { get; set; } = new();
    private int _neededStellplaetze;

    readonly LoadingIndicatorOptions _printPdfViewOptions = new(false, false, false, false);
    readonly LoadingIndicatorOptions _onInitOptions = new(true, false, false, false);

    protected override async Task OnInitializedAsync()
    {
        ConfigurationCommission = await ConfigurationService.GetConfigurationCommissionAsync();
    }

    protected async Task InitializedAsync()
    {
        try
        {
            _possiblePrinter = await Sender.Send(new AllPrinterDescriptionQuery());
            OrderList = [..await Sender.Send(new GetAllOrderInWarehouseQuery())];
            VbrList = [..await Sender.Send(new GetAllVbrsWithStellplaetzeQuery())];
            _allLayoutForDelivery = [..await Sender.Send(new AllRepositoryItemsFromPrintObjectQuery(DeliveryNoteDto.Dummy))];
            _allLayoutForPalett = [..await Sender.Send(new AllRepositoryItemsFromPrintObjectQuery(PalletNoteDto.Dummy))];
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    private async Task OnOrderSelected(WmOrder? order)
    {
        Commission.Order = order;
        if (Commission.Order is null || Commission.Order.Auftragskopf!.KundenNrWE is null)
            return;
        _neededStellplaetze = Commission.Order.PaletLager.Count;
        Commission.Customer = await Sender.Send(new GetCustomerByIdQuery(Commission.Order.Auftragskopf.KundenNrWE.Value));
        Commission.Order.AuftragsposList = [..await Sender.Send(new GetAuftragsPosByOrderNumberQuery(Commission.Order.OrderNumber))];
    }

    private async Task OnSubmit()
    {
        if (Commission.Order is null || Commission.CommissionTrack.Sum(c => c.StellplatzCount) < _neededStellplaetze || Commission.Order.AuftragsposList is null)
            return;
        if (Commission.Order.Auftragskopf!.KundenNrWE is null)
            return;
        await ConfigurationService.SetConfigurationCommissionAsync(ConfigurationCommission);
        try
        {
            var trackindex = 0;
            var amountStellplatzUsed = 0;

            foreach (var paletLager in Commission.Order.PaletLager)
            {
                // Prüfen ob aktuelle Spur voll ist und ggf. zur nächsten wechseln
                if (amountStellplatzUsed >= Commission.CommissionTrack[trackindex].StellplatzCount)
                {
                    trackindex++;
                    amountStellplatzUsed = 0;

                    // Prüfen ob noch eine Spur verfügbar ist
                    if (trackindex >= Commission.CommissionTrack.Count)
                    {
                        throw new Exception("Nicht genügend Stellplätze in den ausgewählten Kommissionsspuren verfügbar");
                    }
                }

                var stellplatzOfPaletLager = await Sender.Send(new GetStellplatzOfPalletLagerQuery(paletLager.Id));
                var hallpositionOfPaletLager = await Sender.Send(new GetHallPositionOfStellplatzQuery(stellplatzOfPaletLager.Id));

                if (Commission.WithRobot)
                {
                    // Verwende die aktuelle Kommissionsspur
                    await Sender.Send(new CreateCommissioningLindeQueueEntryCommand(
                        paletLager.Id,
                        Commission.CommissionTrack[trackindex].Number,
                        hallpositionOfPaletLager));
                }
                else
                {
                    await Sender.Send(new SetManualUnstoredFlagCommand(paletLager.Id));
                }

                var processOrderHeader = await Sender.Send(new GetProcessOrderHeaderOfPaletLaderQuery(paletLager.Id));
                await Publisher.Publish(new CheckStorageStatusNotification(processOrderHeader.Id));
                await Sender.Send(new ClearStellplatzCommand(hallpositionOfPaletLager.HallenPosition, paletLager.Id));

                amountStellplatzUsed++;
            }

            if (!string.IsNullOrEmpty(ConfigurationCommission.Printer))
            {
                await Sender.Send(new PrintPaletLagerDeliveryNoteCommand(
                    Commission.Order.PaletLager,
                    ApplicationPath.GetPath(),
                    Commission.Customer,
                    Commission.Order.Auftragskopf,
                    Commission.Order.AuftragsposList,
                    ConfigurationCommission));
            }

            Commission = new WmCommission();
            _neededStellplaetze = 0;
            if (_onInitialize is not null)
            {
                await _onInitialize.Run();
            }
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    private async Task OpenPrinter(string base64)
    {
        await DialogService.OpenAsync<PrintPage>($"Druckvorschau",
            new Dictionary<string, object>()
            {
                { "PdfDataAsBase64", base64 }
            },
            new DialogOptions()
            {
                Resizable = false,
                Draggable = false,

                Width = "90%",
                Height = "100%"
            });
    }

    private async Task OpenDesignerForLayout()
    {
        try
        {
            IPrintObject objectToPrint = DeliveryNoteDto.Dummy;

            if (ApplicationPath.IAmBlazorServer())
            {
                _ = Task.Run(() => { ListLabelFactory.OpenWebDesigner(objectToPrint, ""); }).ConfigureAwait(false);
            }
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
        finally
        {
            DialogService.Close();
        }
    }

    private async Task OpenDesignerForPalettLayout()
    {
        try
        {
            IPrintObject objectToPrint = PalletNoteDto.Dummy;

            if (ApplicationPath.IAmBlazorServer())
            {
                _ = Task.Run(() => { ListLabelFactory.OpenWebDesigner(objectToPrint, ""); }).ConfigureAwait(false);
            }
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
        finally
        {
            DialogService.Close();
        }
    }

    private async Task ShowPrintViewWithLoading(string layout)
    {
        if (_loadingIndicator is null)
            return;
        Layouttoshow = layout;
        await _loadingIndicator.Run();
    }

    private async Task ShowPrintView()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(Layouttoshow))
                throw new Exception("Es wurde kein Drucklayout ausgewählt");
            var base64 = await Sender.Send(new ShowDeliveryNotePreviewCommand(DeliveryNoteDto.Dummy.PaletLagerList,
                ApplicationPath.GetPath(), DeliveryNoteDto.Dummy.Kunde, DeliveryNoteDto.Dummy.Auftragskopf, DeliveryNoteDto.Dummy.Auftragspos,
                Layouttoshow));
            if (Commission.Order is not null && Commission.Order.Auftragskopf is not null && Commission.Order.AuftragsposList is not null)
            {
                base64 = await Sender.Send(new ShowDeliveryNotePreviewCommand(Commission.Order.PaletLager,
                    ApplicationPath.GetPath(), Commission.Customer, Commission.Order.Auftragskopf, Commission.Order.AuftragsposList,
                    Layouttoshow));
            }

            if (!string.IsNullOrEmpty(base64))
                await OpenPrinter(base64);
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    private async Task ShowPalettPrintViewWithLoading(string layout)
    {
        if (_loadingIndicator is null)
            return;
        Layouttoshow = layout;
        await _loadingIndicator.Run();
    }

    private async Task ShowPalettPrintView()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(Layouttoshow))
                throw new Exception("Es wurde kein Drucklayout ausgewählt");
            var base64 = await Sender.Send(new ShowPaletNotePreviewCommand(PalletNoteDto.Dummy.PaletLagerList,
                ApplicationPath.GetPath(), PalletNoteDto.Dummy.Kunde, PalletNoteDto.Dummy.Auftragskopf, PalletNoteDto.Dummy.Auftragspos,
                Layouttoshow));
            if (Commission.Order is not null && Commission.Order.Auftragskopf is not null && Commission.Order.AuftragsposList is not null)
            {
                base64 = await Sender.Send(new ShowPaletNotePreviewCommand(Commission.Order.PaletLager,
                    ApplicationPath.GetPath(), Commission.Customer, Commission.Order.Auftragskopf, Commission.Order.AuftragsposList,
                    Layouttoshow));
            }

            if (!string.IsNullOrEmpty(base64))
                await OpenPrinter(base64);
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    bool _saveConfigDataStart;

    private async Task SaveConfigData()
    {
        if (_saveConfigDataStart)
            return;

        _saveConfigDataStart = true;
        try
        {
            await ConfigurationService.SetConfigurationCommissionAsync(ConfigurationCommission);
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
        finally
        {
            _saveConfigDataStart = false;
        }
    }

    private async Task Print()
    {
        if (Commission.Order is null)
            return;
        if (!string.IsNullOrEmpty(ConfigurationCommission.Printer) && !string.IsNullOrEmpty(ConfigurationCommission.LayoutForDeliveryNote) && !string.IsNullOrEmpty(ConfigurationCommission.LayoutForPallet))
        {
            await Sender.Send(new PrintPaletLagerDeliveryNoteCommand(
                Commission.Order.PaletLager,
                ApplicationPath.GetPath(),
                Commission.Customer,
                Commission.Order.Auftragskopf,
                Commission.Order.AuftragsposList,
                ConfigurationCommission));
        }
    }

}
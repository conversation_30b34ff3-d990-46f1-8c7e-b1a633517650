namespace WinGng.RazorLib.Components.Common.LoadingPage;

public class LoadingIndicatorOptions
{
    public LoadingIndicatorOptions(
    bool startAfterRender = false,
    bool showStepNumbers = false,
    bool showPercentage = true,
    bool showText = true,
    int currentStep = 0,
    int totalSteps = 1,
    string text = "Loading...")
    {
        StartAfterRender = startAfterRender;
        ShowStepNumbers = showStepNumbers;
        ShowPercentage = showPercentage;
        ShowText = showText;
        CurrentStep = currentStep;
        TotalSteps = totalSteps;
        Text = text;
    }
    
    public Action? UpdateAction { get; set; }

    private bool _startAfterRender;

    public bool StartAfterRender
    {
        get => _startAfterRender;
        set
        {
            _startAfterRender = value;
            if (UpdateAction is not null) UpdateAction.Invoke();
        }
    }

    private bool _showStepNumbers;

    public bool ShowStepNumbers
    {
        get => _showStepNumbers;
        set
        {
            _showStepNumbers = value;
            if (UpdateAction is not null) UpdateAction.Invoke();
        }
    }

    private bool _showPercentage;

    public bool ShowPercentage
    {
        get => _showPercentage;
        set
        {
            _showPercentage = value;
            if (UpdateAction is not null) UpdateAction.Invoke();
        }
    }

    private int _currentStep;

    public int CurrentStep
    {
        get => _currentStep;
        set
        {
            _currentStep = value;
            CurrentPercent = (int)(((float)_currentStep / (float)_totalSteps) * 100);
            if (UpdateAction is not null) UpdateAction.Invoke();
        }
    }

    private int _currentPercent;

    public int CurrentPercent
    {
        get => _currentPercent;
        set
        {
            _currentPercent = value;
            if (UpdateAction is not null) UpdateAction.Invoke();
        }
    }

    private int _totalSteps = 1;

    public int TotalSteps
    {
        get => _totalSteps;
        set
        {
            _totalSteps = value;
            if (UpdateAction is not null) UpdateAction.Invoke();
        }
    }

    private bool _showText;

    public bool ShowText
    {
        get => _showText;
        set
        {
            _showText = value;
            if (UpdateAction is not null) UpdateAction.Invoke();
        }
    }

    private string _text = "Loading...";

    public string Text
    {
        get => _text;
        set
        {
            _text = value;
            if (UpdateAction is not null) UpdateAction.Invoke();
        }
    }
    
    
    private bool _showOwnStepText;

    public bool ShowOwnStepText
    {
        get => _showOwnStepText;
        set
        {
            _showOwnStepText = value;
            if (UpdateAction is not null) UpdateAction.Invoke();
        }
    }

    private string _ownStepText = "Loading...";

    public string OwnStepText
    {
        get => _ownStepText;
        set
        {
            _ownStepText = value;
            if (UpdateAction is not null) UpdateAction.Invoke();
        }
    }
}
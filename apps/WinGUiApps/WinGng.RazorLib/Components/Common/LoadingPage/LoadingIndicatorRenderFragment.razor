<style>
    .vas-loader-wrapper {
        position: relative;
        width: 140px;
        height: 140px;
        transform-style: preserve-3d;
        animation: vas-spin 6s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
        margin: 0 auto;
    }

    .vas-logo-block {
        position: absolute;
        border-radius: 20px;
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    /* Block 1 - Top Left (Light Purple) */
    .vas-block-1 {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #9F9FED 0%, #B8B8F3 100%);
        top: 0;
        left: 0;
        animation: vas-separateTopLeft 6s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
    }

    /* Block 2 - Top Right (Medium Purple) */
    .vas-block-2 {
        width: 70px;
        height: 60px;
        background: linear-gradient(135deg, #7B7BC5 0%, #8E8ED4 100%);
        top: 0;
        right: 0;
        animation: vas-separateTopRight 6s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
    }

    /* Block 3 - Bottom Left (Dark Purple) */
    .vas-block-3 {
        width: 85px;
        height: 70px;
        background: linear-gradient(135deg, #3B3B5C 0%, #4A4A70 100%);
        bottom: 0;
        left: 0;
        animation: vas-separateBottomLeft 6s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
    }

    /* Block 4 - Bottom Right (Medium-Dark Purple) */
    .vas-block-4 {
        width: 45px;
        height: 70px;
        background: linear-gradient(135deg, #6565A0 0%, #7575B0 100%);
        bottom: 0;
        right: 0;
        animation: vas-separateBottomRight 6s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
    }

    .vas-loading-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #333;
        font-weight: bold;
        font-size: 14px;
        text-align: center;
        z-index: 10;
        background: rgba(255, 255, 255, 0.9);
        padding: 4px 8px;
        border-radius: 8px;
        white-space: nowrap;
    }

    /* Main rotation animation */
    @@keyframes vas-spin {
        0% {
            transform: rotate(0deg) scale(1);
        }
        20% {
            transform: rotate(90deg) scale(1.05);
        }
        35% {
            transform: rotate(180deg) scale(1.1);
        }
        50% {
            transform: rotate(360deg) scale(1.15);
        }
        65% {
            transform: rotate(450deg) scale(1.1);
        }
        80% {
            transform: rotate(540deg) scale(1.05);
        }
        100% {
            transform: rotate(720deg) scale(1);
        }
    }

    /* Separation animations */
    @@keyframes vas-separateTopLeft {
        0%, 100% {
            transform: translate(0, 0) rotate(0deg) scale(1);
            opacity: 1;
        }
        20% {
            transform: translate(-5px, -5px) rotate(5deg) scale(1.02);
            opacity: 0.95;
        }
        35% {
            transform: translate(-12px, -12px) rotate(10deg) scale(1.05);
            opacity: 0.9;
        }
        50% {
            transform: translate(-20px, -20px) rotate(15deg) scale(1.08);
            opacity: 0.85;
        }
        65% {
            transform: translate(-12px, -12px) rotate(10deg) scale(1.05);
            opacity: 0.9;
        }
        80% {
            transform: translate(-5px, -5px) rotate(5deg) scale(1.02);
            opacity: 0.95;
        }
    }

    @@keyframes vas-separateTopRight {
        0%, 100% {
            transform: translate(0, 0) rotate(0deg) scale(1);
            opacity: 1;
        }
        20% {
            transform: translate(5px, -5px) rotate(-5deg) scale(1.02);
            opacity: 0.95;
        }
        35% {
            transform: translate(12px, -12px) rotate(-10deg) scale(1.05);
            opacity: 0.9;
        }
        50% {
            transform: translate(20px, -20px) rotate(-15deg) scale(1.08);
            opacity: 0.85;
        }
        65% {
            transform: translate(12px, -12px) rotate(-10deg) scale(1.05);
            opacity: 0.9;
        }
        80% {
            transform: translate(5px, -5px) rotate(-5deg) scale(1.02);
            opacity: 0.95;
        }
    }

    @@keyframes vas-separateBottomLeft {
        0%, 100% {
            transform: translate(0, 0) rotate(0deg) scale(1);
            opacity: 1;
        }
        20% {
            transform: translate(-5px, 5px) rotate(-5deg) scale(1.02);
            opacity: 0.95;
        }
        35% {
            transform: translate(-12px, 12px) rotate(-10deg) scale(1.05);
            opacity: 0.9;
        }
        50% {
            transform: translate(-20px, 20px) rotate(-15deg) scale(1.08);
            opacity: 0.85;
        }
        65% {
            transform: translate(-12px, 12px) rotate(-10deg) scale(1.05);
            opacity: 0.9;
        }
        80% {
            transform: translate(-5px, 5px) rotate(-5deg) scale(1.02);
            opacity: 0.95;
        }
    }

    @@keyframes vas-separateBottomRight {
        0%, 100% {
            transform: translate(0, 0) rotate(0deg) scale(1);
            opacity: 1;
        }
        20% {
            transform: translate(5px, 5px) rotate(5deg) scale(1.02);
            opacity: 0.95;
        }
        35% {
            transform: translate(12px, 12px) rotate(10deg) scale(1.05);
            opacity: 0.9;
        }
        50% {
            transform: translate(20px, 20px) rotate(15deg) scale(1.08);
            opacity: 0.85;
        }
        65% {
            transform: translate(12px, 12px) rotate(10deg) scale(1.05);
            opacity: 0.9;
        }
        80% {
            transform: translate(5px, 5px) rotate(5deg) scale(1.02);
            opacity: 0.95;
        }
    }
</style>

<RadzenStack @ref=LoadingElement Orientation="Orientation.Vertical" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" Wrap="FlexWrap.Wrap" Class="rz-m-12" Gap="2rem">
    <div style="position: relative; display: inline-block;">
        <div class="vas-loader-wrapper">
            <div class="vas-logo-block vas-block-1"></div>
            <div class="vas-logo-block vas-block-2"></div>
            <div class="vas-logo-block vas-block-3"></div>
            <div class="vas-logo-block vas-block-4"></div>
        </div>
        @if (Option.ShowStepNumbers || Option.ShowPercentage)
        {
            <div class="vas-loading-text">
                @if (Option.ShowStepNumbers)
                {
                    @($"{Option.CurrentStep} / {Option.TotalSteps}")
                }
                @if (Option.ShowPercentage)
                {
                    @($"{Option.CurrentPercent}%")
                }
            </div>
        }
    </div>
    @if (Option.ShowText)
    {
        @($"{Option.Text}")
    }
    @if (Option.ShowOwnStepText)
    {
        @(string.Format(Option.OwnStepText, Option.CurrentStep, Option.TotalSteps));
    }
</RadzenStack>

@code {

    [Parameter] public LoadingIndicatorOptions Option { get; set; } = new();
    public RadzenStack? LoadingElement { get; set; }

    protected override void OnInitialized()
    {
        Option.UpdateAction = StateHasChanged;
    }
}
using Languages;
using Microsoft.AspNetCore.Localization;

namespace WinGng.WebApp.Middleware;

public class CultureMiddleware(RequestDelegate next, IConfiguration configuration)
{
    public async Task InvokeAsync(HttpContext context)
    {
        // Check if culture cookie exists
        var cultureCookieName = CookieRequestCultureProvider.DefaultCookieName;
        var cultureCookieValue = context.Request.Cookies[cultureCookieName];
        
        var cookieOptions = new CookieOptions
        {
            Path = "/",
            SameSite = SameSiteMode.Lax,
            IsEssential = true,
            Expires = DateTimeOffset.MaxValue
        }; 
        
        if (string.IsNullOrEmpty(cultureCookieValue))
        {
            // Get default culture from configuration
            var defaultCulture = configuration["DefaultCulture"] ?? "en-US";
                
            // Ensure the default culture is supported
            if (LanguageResource.SupportedCultures.All(c => c.Name != defaultCulture))
                defaultCulture = "en-US";

            // Set the culture cookie
            var defaultCookieValue = CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(defaultCulture));
            context.Response.Cookies.Append(cultureCookieName, defaultCookieValue, cookieOptions);
        }
        else
        {
            // Append the same cookie with updated expiration
            context.Response.Cookies.Append(cultureCookieName, cultureCookieValue, cookieOptions); 
        }

        await next(context);
    }
}